/*******************************************************************************
 * @file      scheduler.c
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     一个简单的前后台任务调度器实现 - STM32H743IIT6版本
 * @note      此调度器实现了一个基础的非抢占式、时间片轮转调度策略。它适用于
 * 裸机（无操作系统）环境。H7版本增加了性能监控、任务状态管理等功能，
 * 充分利用H7的高性能特性。
 *******************************************************************************/

#include "scheduler.h"
#include "key_app.h"
#include "AD9959.h"

/*******************************************************************************
 * 私有变量定义
 *******************************************************************************/

// 全局变量，用于存储在任务列表中定义的任务数量
uint8_t task_num;

// 系统负载统计
static uint32_t total_idle_cycles = 0;
static uint32_t total_busy_cycles = 0;
static uint32_t last_load_calc_time = 0;

/**
 * @brief 增强的任务控制块结构体定义
 * @note  每个任务都由一个该结构体来描述，H7版本增加了性能监控功能
 */
typedef struct
{
    void (*task_func)(void);    // 任务函数的指针，指向需要被调度的具体函数
    uint32_t rate_ms;           // 任务执行的周期，单位：毫秒(ms)
    uint32_t last_run;          // 上一次任务执行的时间戳 (由HAL_GetTick()提供)
    task_state_t state;         // 任务状态
    task_perf_t performance;    // 任务性能统计
    uint32_t stack_size;        // 任务栈大小 (预留)
    uint32_t stack_used;        // 已使用栈大小 (预留)
} enhanced_task_t;

/*******************************************************************************
 * 任务函数声明
 *******************************************************************************/

/**
 * @brief  串口处理任务示例
 * @details 此函数作为一个示例任务，通过串口1打印当前的电压反馈值和PID输出值，
 * 常用于系统调试和状态监控。
 * @param  None
 * @retval None
 */
void uart_proc(void)
{
    // 调用自定义的printf函数，将浮点数和整数格式化后通过UART1发送
    // `vol_amp2/2*10` 是反馈值，`output` 是控制器输出值
    my_printf(&huart1, "%f,%d\r\n", vol_amp2 / 2 * 10, output);
}

/**
 * @brief  系统性能监控任务
 * @details 监控系统负载、任务性能等信息，并通过串口输出
 * @param  None
 * @retval None
 */
void system_monitor_proc(void)
{
    static uint32_t monitor_counter = 0;
    
    monitor_counter++;
    
    // 每10秒输出一次系统状态
    if (monitor_counter >= 100) { // 100ms * 100 = 10s
        monitor_counter = 0;
        
        uint8_t cpu_load = scheduler_get_cpu_load();
        my_printf(&huart3, "[SYS] CPU Load: %d%%, Free Heap: %d bytes\r\n", 
                  cpu_load, xPortGetFreeHeapSize());
        
        // 输出各任务性能统计
        for (uint8_t i = 0; i < task_num; i++) {
            task_perf_t perf;
            if (scheduler_get_task_performance(i, &perf) == 0) {
                my_printf(&huart3, "[TASK%d] Runs: %lu, Avg: %lu cycles\r\n", 
                          i, perf.run_count, perf.avg_cycles);
            }
        }
    }
}

/*******************************************************************************
 * 静态任务列表
 *******************************************************************************/

/**
 * @brief  静态任务列表
 * @note   这是调度器的核心任务清单。所有需要被调度的任务都在此数组中定义。
 * 每一行代表一个任务，格式为：{ 任务函数名, 执行周期(ms), 初始上次运行时间, 任务状态, 性能统计, 栈信息 }
 * 通过注释或取消注释某一行，可以方便地启用或禁用对应的任务。
 */
static enhanced_task_t scheduler_task[] =
{
    //  { function,                    rate_ms, last_run, state,           performance, stack_size, stack_used }
    {key_proc,                        10,      0,        TASK_STATE_READY, {0},         0,          0}, // 按键扫描处理任务，每10ms执行一次
    {system_monitor_proc,             100,     0,        TASK_STATE_READY, {0},         0,          0}, // 系统监控任务，每100ms执行一次
    //{ad_proc,                       1,       0,        TASK_STATE_READY, {0},         0,          0}, // ADC采样处理任务，每1ms执行一次
    //{AD9959_Modulation_State_Update, 5,      0,        TASK_STATE_READY, {0},         0,          0}, // 调制状态机更新任务，每5ms执行一次
    //{freq_proc,                     1000,    0,        TASK_STATE_READY, {0},         0,          0}, // 频率处理任务，每1000ms执行一次
    //{wave_test,                     20,      0,        TASK_STATE_READY, {0},         0,          0}, // 波形测试任务
    //{AD9959_proc,                   1200,    0,        TASK_STATE_READY, {0},         0,          0}, // AD9959 DDS芯片处理任务
    //{uart_proc,                     10,      0,        TASK_STATE_READY, {0},         0,          0}, // 串口调试信息打印任务
    //{Pid_Proc,                      1,       0,        TASK_STATE_READY, {0},         0,          0}, // PID控制器处理任务
};

/*******************************************************************************
 * 公共函数实现
 *******************************************************************************/

/**
 * @brief  调度器初始化函数
 * @details 此函数计算在任务列表中定义的任务总数，并初始化性能监控。
 * 必须在系统启动时调用一次，先于 `scheduler_run()` 的首次调用。
 * @param  None
 * @retval None
 */
void scheduler_init(void)
{
    // 通过 `sizeof` 运算符计算出数组中的元素个数，并存入全局变量 `task_num`
    task_num = sizeof(scheduler_task) / sizeof(enhanced_task_t);
    
    // 初始化性能监控
    for (uint8_t i = 0; i < task_num; i++) {
        scheduler_task[i].performance.min_cycles = 0xFFFFFFFF; // 初始化为最大值
        scheduler_task[i].state = TASK_STATE_READY;
    }
    
    // 初始化负载统计
    last_load_calc_time = HAL_GetTick();
    
    DBG_PRINTF("Scheduler initialized with %d tasks", task_num);
}

/**
 * @brief  调度器主运行函数
 * @details 这是调度器的核心执行函数，需要被放置在`main`函数的`while(1)`主循环中
 * 不断地被调用。它会遍历整个任务列表，检查每个任务是否到达了执行时间。
 * H7版本增加了性能监控功能。
 * @param  None
 * @retval None
 */
void scheduler_run(void)
{
    uint32_t idle_start = PERF_COUNTER_GET();
    
    // 遍历任务列表中的每一个任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 跳过挂起或阻塞的任务
        if (scheduler_task[i].state != TASK_STATE_READY) {
            continue;
        }
        
        // 获取当前系统的时间戳（从系统启动开始的毫秒数）
        uint32_t now_time = HAL_GetTick();

        // 判断当前任务是否到达执行时间
        // 判定条件为：当前时间 >= 上次运行时间 + 任务周期
        // 这个判断方式可以处理 HAL_GetTick() 计时器回绕（溢出）的情况
        if (now_time - scheduler_task[i].last_run >= scheduler_task[i].rate_ms)
        {
            // 更新任务的上次运行时间为当前时间，为下一次调度做准备
            scheduler_task[i].last_run = now_time;
            
            // 标记任务为运行状态
            scheduler_task[i].state = TASK_STATE_RUNNING;
            
            // 开始性能计数
            uint32_t start_cycles = PERF_COUNTER_GET();
            
            // 通过函数指针调用对应的任务函数
            scheduler_task[i].task_func();
            
            // 结束性能计数并更新统计
            uint32_t end_cycles = PERF_COUNTER_GET();
            uint32_t task_cycles = end_cycles - start_cycles;
            
            // 更新性能统计
            scheduler_task[i].performance.run_count++;
            scheduler_task[i].performance.total_cycles += task_cycles;
            
            if (task_cycles > scheduler_task[i].performance.max_cycles) {
                scheduler_task[i].performance.max_cycles = task_cycles;
            }
            
            if (task_cycles < scheduler_task[i].performance.min_cycles) {
                scheduler_task[i].performance.min_cycles = task_cycles;
            }
            
            scheduler_task[i].performance.avg_cycles = 
                scheduler_task[i].performance.total_cycles / scheduler_task[i].performance.run_count;
            
            // 恢复任务为就绪状态
            scheduler_task[i].state = TASK_STATE_READY;
            
            // 累计忙碌周期
            total_busy_cycles += task_cycles;
        }
    }
    
    // 累计空闲周期
    uint32_t idle_end = PERF_COUNTER_GET();
    total_idle_cycles += (idle_end - idle_start);
}

/**
 * @brief  获取任务性能统计信息
 * @param  task_id 任务ID
 * @param  perf 性能统计结构指针
 * @retval 0: 成功, -1: 失败
 */
int scheduler_get_task_performance(uint8_t task_id, task_perf_t *perf)
{
    if (task_id >= task_num || perf == NULL) {
        return -1;
    }
    
    *perf = scheduler_task[task_id].performance;
    return 0;
}

/**
 * @brief  重置任务性能统计
 * @param  task_id 任务ID (0xFF表示重置所有任务)
 */
void scheduler_reset_performance(uint8_t task_id)
{
    if (task_id == 0xFF) {
        // 重置所有任务
        for (uint8_t i = 0; i < task_num; i++) {
            memset(&scheduler_task[i].performance, 0, sizeof(task_perf_t));
            scheduler_task[i].performance.min_cycles = 0xFFFFFFFF;
        }
        total_idle_cycles = 0;
        total_busy_cycles = 0;
    } else if (task_id < task_num) {
        // 重置指定任务
        memset(&scheduler_task[task_id].performance, 0, sizeof(task_perf_t));
        scheduler_task[task_id].performance.min_cycles = 0xFFFFFFFF;
    }
}

/**
 * @brief  挂起指定任务
 * @param  task_id 任务ID
 * @retval 0: 成功, -1: 失败
 */
int scheduler_suspend_task(uint8_t task_id)
{
    if (task_id >= task_num) {
        return -1;
    }
    
    scheduler_task[task_id].state = TASK_STATE_SUSPENDED;
    return 0;
}

/**
 * @brief  恢复指定任务
 * @param  task_id 任务ID
 * @retval 0: 成功, -1: 失败
 */
int scheduler_resume_task(uint8_t task_id)
{
    if (task_id >= task_num) {
        return -1;
    }
    
    scheduler_task[task_id].state = TASK_STATE_READY;
    return 0;
}

/**
 * @brief  获取系统负载百分比
 * @retval 系统负载 (0-100)
 */
uint8_t scheduler_get_cpu_load(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // 每秒计算一次负载
    if (current_time - last_load_calc_time >= 1000) {
        uint32_t total_cycles = total_idle_cycles + total_busy_cycles;
        uint8_t load = 0;
        
        if (total_cycles > 0) {
            load = (total_busy_cycles * 100) / total_cycles;
        }
        
        // 重置计数器
        total_idle_cycles = 0;
        total_busy_cycles = 0;
        last_load_calc_time = current_time;
        
        return load;
    }
    
    return 0; // 数据不足，返回0
}
