/*******************************************************************************
 * @file      freq_measure.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     频率测量模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __FREQ_MEASURE_H__
#define __FREQ_MEASURE_H__

#include "bsp_system.h"

// 函数声明
void freq_measure_init(void);
void freq_proc(void);
float get_frequency_value(void);

#endif /* __FREQ_MEASURE_H__ */
