/*******************************************************************************
 * @file      phase_measure.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     相位测量模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __PHASE_MEASURE_H__
#define __PHASE_MEASURE_H__

#include "bsp_system.h"

// 函数声明
void phase_measure_init(void);
float get_phase_difference(void);

#endif /* __PHASE_MEASURE_H__ */
