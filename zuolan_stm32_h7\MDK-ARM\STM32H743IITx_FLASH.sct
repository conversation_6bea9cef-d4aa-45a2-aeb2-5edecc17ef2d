; *************************************************************
; *** Scatter-Loading Description File for STM32H743IITx ***
; *************************************************************
; STM32H743IIT6 Memory Layout:
; - Flash: 2MB (0x08000000 - 0x081FFFFF)
; - DTCM RAM: 128KB (0x20000000 - 0x2001FFFF) - Fastest access
; - AXI SRAM: 512KB (0x24000000 - 0x2407FFFF) - DMA accessible
; - SRAM1: 128KB (0x30000000 - 0x3001FFFF)
; - SRAM2: 128KB (0x30020000 - 0x3003FFFF)
; - SRAM3: 32KB (0x30040000 - 0x30047FFF)
; - SRAM4: 64KB (0x38000000 - 0x3800FFFF) - Backup domain

LR_IROM1 0x08000000 0x00200000  {    ; load region size_region (2MB Flash)
  
  ; Execution region for code and constants
  ER_IROM1 0x08000000 0x00200000  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   .ANY (+XO)
  }
  
  ; DTCM RAM - Fastest access, ideal for critical data and stack
  RW_IRAM1 0x20000000 0x00020000  {  ; RW data, 128KB DTCM
   .ANY (+RW +ZI)
  }
  
  ; AXI SRAM - DMA accessible, ideal for large buffers
  RW_IRAM2 0x24000000 0x00080000  {  ; 512KB AXI SRAM
   *(.axi_sram)                      ; Custom section for AXI SRAM
   *(fft_large_buffers)              ; Large FFT buffers
   *(dma_buffers)                    ; DMA buffers
  }
  
  ; SRAM1 - General purpose
  RW_IRAM3 0x30000000 0x00020000  {  ; 128KB SRAM1
   *(.sram1)                         ; Custom section for SRAM1
   *(algorithm_buffers)              ; Algorithm working buffers
  }
  
  ; SRAM2 - General purpose
  RW_IRAM4 0x30020000 0x00020000  {  ; 128KB SRAM2
   *(.sram2)                         ; Custom section for SRAM2
   *(communication_buffers)          ; Communication buffers
  }
  
  ; SRAM3 - Small buffers
  RW_IRAM5 0x30040000 0x00008000  {  ; 32KB SRAM3
   *(.sram3)                         ; Custom section for SRAM3
   *(small_buffers)                  ; Small working buffers
  }
  
  ; SRAM4 - Backup domain (optional, for persistent data)
  RW_IRAM6 0x38000000 0x00010000  {  ; 64KB SRAM4
   *(.backup_sram)                   ; Custom section for backup SRAM
   *(persistent_data)                ; Persistent data across resets
  }
}

; Memory usage optimization notes:
; 1. DTCM (0x20000000): Fastest access, use for stack, heap, and critical variables
; 2. AXI SRAM (0x24000000): DMA accessible, use for large data buffers
; 3. SRAM1-3 (0x30000000-0x30047FFF): General purpose, good for algorithm data
; 4. SRAM4 (0x38000000): Backup domain, retains data in standby mode

; Performance optimization:
; - Place frequently accessed data in DTCM
; - Place DMA buffers in AXI SRAM
; - Use memory sections to control placement of specific data structures
