/*******************************************************************************
 * @file      ad_measure.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     AD采样测量模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __AD_MEASURE_H__
#define __AD_MEASURE_H__

#include "bsp_system.h"

// 全局变量声明
extern float vol_amp2;

// 函数声明
void ad_measure_init(void);
void ad_proc(void);
float get_voltage_value(uint16_t adc_value);

#endif /* __AD_MEASURE_H__ */
