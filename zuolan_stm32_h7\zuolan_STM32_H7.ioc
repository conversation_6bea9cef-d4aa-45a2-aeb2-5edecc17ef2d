#MicroXplorer Configuration settings - do not modify
DAC.DAC_OutputBuffer=DAC_OUTPUTBUFFER_ENABLE
DAC.DAC_OutputBuffer2=DAC_OUTPUTBUFFER_ENABLE
DAC.IPParameters=DAC_OutputBuffer2,DAC_OutputBuffer
FMC.AddressHoldTime2=1
FMC.AddressSetupTime2=3
FMC.BusTurnAroundDuration2=0
FMC.DataSetupTime2=6
FMC.IPParameters=AddressSetupTime2,DataSetupTime2,BusTurnAroundDuration2,AddressHoldTime2,WriteOperation2
FMC.WriteOperation2=FMC_WRITE_OPERATION_ENABLE
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32H743IIT6
Mcu.Family=STM32H7
Mcu.IP0=CORTEX_M7
Mcu.IP1=DAC1
Mcu.IP2=FMC
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=USART1
Mcu.IP7=USART2
Mcu.IP8=USART3
Mcu.IPNb=9
Mcu.Name=STM32H743IITx
Mcu.Package=LQFP176
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin10=PE10
Mcu.Pin11=PE11
Mcu.Pin12=PE12
Mcu.Pin13=PE13
Mcu.Pin14=PE14
Mcu.Pin15=PE15
Mcu.Pin16=PB10
Mcu.Pin17=PB11
Mcu.Pin18=PB12
Mcu.Pin19=PB13
Mcu.Pin2=PC0
Mcu.Pin20=PB14
Mcu.Pin21=PB15
Mcu.Pin22=PD8
Mcu.Pin23=PD9
Mcu.Pin24=PD10
Mcu.Pin25=PD11
Mcu.Pin26=PD12
Mcu.Pin27=PD13
Mcu.Pin28=PD14
Mcu.Pin29=PD15
Mcu.Pin3=PC1
Mcu.Pin30=PC6
Mcu.Pin31=PC7
Mcu.Pin32=PC8
Mcu.Pin33=PC9
Mcu.Pin34=PA8
Mcu.Pin35=PA9
Mcu.Pin36=PA10
Mcu.Pin37=PA11
Mcu.Pin38=PA12
Mcu.Pin39=PA13
Mcu.Pin4=PC2
Mcu.Pin40=PA14
Mcu.Pin41=PA15
Mcu.Pin42=PC10
Mcu.Pin43=PC11
Mcu.Pin44=PC12
Mcu.Pin45=PD0
Mcu.Pin46=PD1
Mcu.Pin47=PD2
Mcu.Pin48=PD3
Mcu.Pin49=PD4
Mcu.Pin5=PC3
Mcu.Pin50=PD5
Mcu.Pin51=PD6
Mcu.Pin52=PD7
Mcu.Pin53=PG9
Mcu.Pin54=PG10
Mcu.Pin55=PG11
Mcu.Pin56=PG12
Mcu.Pin57=PG13
Mcu.Pin58=PG14
Mcu.Pin59=PG15
Mcu.Pin6=PA4
Mcu.Pin60=PB3
Mcu.Pin61=PB4
Mcu.Pin62=PB5
Mcu.Pin63=PB6
Mcu.Pin64=PB7
Mcu.Pin65=PB8
Mcu.Pin66=PB9
Mcu.Pin67=PE0
Mcu.Pin68=PE1
Mcu.Pin69=PI0
Mcu.Pin7=PA5
Mcu.Pin70=PI5
Mcu.Pin71=PinOutPanel.RotationAngle
Mcu.Pin8=PE7
Mcu.Pin9=PE9
Mcu.PinsNb=72
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H743IITx
MxCube.Version=6.12.0
MxDb.Version=DB.6.0.120
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA11.GPIO_Label=AD9959_IOUPDATE
PA11.GPIO_PuPd=GPIO_PULLUP
PA11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA11.Locked=true
PA11.Signal=GPIO_Output
PA12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA12.GPIO_Label=AD9959_RESET
PA12.GPIO_PuPd=GPIO_PULLUP
PA12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA12.Locked=true
PA12.Signal=GPIO_Output
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA15.GPIO_Label=AD9959_CS
PA15.GPIO_PuPd=GPIO_PULLUP
PA15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA15.Locked=true
PA15.Signal=GPIO_Output
PA4.Signal=COMP_DAC11_group
PA5.Signal=COMP_DAC12_group
PA8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA8.GPIO_Label=AD9959_SCLK
PA8.GPIO_PuPd=GPIO_PULLUP
PA8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB12.GPIO_Label=AD9959_SDIO0
PB12.GPIO_PuPd=GPIO_PULLUP
PB12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB13.GPIO_Label=AD9959_SDIO1
PB13.GPIO_PuPd=GPIO_PULLUP
PB13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB14.GPIO_Label=AD9959_SDIO2
PB14.GPIO_PuPd=GPIO_PULLUP
PB14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB15.GPIO_Label=AD9959_SDIO3
PB15.GPIO_PuPd=GPIO_PULLUP
PB15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB15.Locked=true
PB15.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB3.GPIO_Label=KEY1
PB3.GPIO_PuPd=GPIO_PULLUP
PB3.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB3.Locked=true
PB3.Signal=GPIO_Input
PB4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB4.GPIO_Label=KEY2
PB4.GPIO_PuPd=GPIO_PULLUP
PB4.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB4.Locked=true
PB4.Signal=GPIO_Input
PB5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB5.GPIO_Label=KEY3
PB5.GPIO_PuPd=GPIO_PULLUP
PB5.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB5.Locked=true
PB5.Signal=GPIO_Input
PB6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB6.GPIO_Label=KEY4
PB6.GPIO_PuPd=GPIO_PULLUP
PB6.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB6.Locked=true
PB6.Signal=GPIO_Input
PB7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB7.GPIO_Label=KEY5
PB7.GPIO_PuPd=GPIO_PULLUP
PB7.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB7.Locked=true
PB7.Signal=GPIO_Input
PB8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB8.GPIO_Label=KEY6
PB8.GPIO_PuPd=GPIO_PULLUP
PB8.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB8.Locked=true
PB8.Signal=GPIO_Input
PB9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB9.GPIO_Label=KEY7
PB9.GPIO_PuPd=GPIO_PULLUP
PB9.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB9.Locked=true
PB9.Signal=GPIO_Input
PC0.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC0.GPIO_Label=LED1
PC0.GPIO_PuPd=GPIO_NOPULL
PC0.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC0.Locked=true
PC0.Signal=GPIO_Output
PC1.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC1.GPIO_Label=LED2
PC1.GPIO_PuPd=GPIO_NOPULL
PC1.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC1.Locked=true
PC1.Signal=GPIO_Output
PC10.Mode=Asynchronous
PC10.Signal=USART3_TX
PC11.Mode=Asynchronous
PC11.Signal=USART3_RX
PC12.Mode=Asynchronous
PC12.Signal=USART5_TX
PC2.Mode=Asynchronous
PC2.Signal=USART2_TX
PC3.Mode=Asynchronous
PC3.Signal=USART2_RX
PC6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC6.GPIO_Label=LED3
PC6.GPIO_PuPd=GPIO_NOPULL
PC6.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC7.GPIO_Label=LED4
PC7.GPIO_PuPd=GPIO_NOPULL
PC7.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC7.Locked=true
PC7.Signal=GPIO_Output
PC8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC8.GPIO_Label=LED5
PC8.GPIO_PuPd=GPIO_NOPULL
PC8.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC8.Locked=true
PC8.Signal=GPIO_Output
PC9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC9.GPIO_Label=LED6
PC9.GPIO_PuPd=GPIO_NOPULL
PC9.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC9.Locked=true
PC9.Signal=GPIO_Output
PD0.Signal=FMC_D2
PD1.Signal=FMC_D3
PD10.Signal=FMC_D15
PD11.Signal=FMC_A16_CLE
PD12.Signal=FMC_A17_ALE
PD13.Signal=FMC_A18
PD14.Signal=FMC_D0
PD15.Signal=FMC_D1
PD2.Mode=Asynchronous
PD2.Signal=USART5_RX
PD3.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD3.GPIO_Label=LED7
PD3.GPIO_PuPd=GPIO_NOPULL
PD3.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD3.Locked=true
PD3.Signal=GPIO_Output
PD4.Signal=FMC_NOE
PD5.Signal=FMC_NWE
PD6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD6.GPIO_Label=LED8
PD6.GPIO_PuPd=GPIO_NOPULL
PD6.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD6.Locked=true
PD6.Signal=GPIO_Output
PD7.Signal=FMC_NE1
PD8.Signal=FMC_D13
PD9.Signal=FMC_D14
PE0.Signal=FMC_NBL0
PE1.Signal=FMC_NBL1
PE10.Signal=FMC_D7
PE11.Signal=FMC_D8
PE12.Signal=FMC_D9
PE13.Signal=FMC_D10
PE14.Signal=FMC_D11
PE15.Signal=FMC_D12
PE7.Signal=FMC_D4
PE8.Signal=FMC_D5
PE9.Signal=FMC_D6
PG10.Signal=FMC_NE3
PG11.Signal=FMC_NCE4_2
PG12.Signal=FMC_NE4
PG13.Signal=FMC_A24
PG14.Signal=FMC_A25
PG15.Signal=FMC_SDNCAS
PG9.Signal=FMC_NCE3_2
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PI0.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PI0.GPIO_Label=AD9959_P0
PI0.GPIO_PuPd=GPIO_PULLUP
PI0.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI0.Locked=true
PI0.Signal=GPIO_Output
PI5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PI5.GPIO_Label=AD9959_SDIO3
PI5.GPIO_PuPd=GPIO_PULLUP
PI5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI5.Locked=true
PI5.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H743IITx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.11.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=zuolan_STM32_H7.ioc
ProjectManager.ProjectName=zuolan_STM32_H7
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_FMC_Init-FMC-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_USART2_UART_Init-USART2-false-HAL-true,6-MX_USART3_UART_Init-USART3-false-HAL-true,7-MX_DAC1_Init-DAC1-false-HAL-true
RCC.ADCFreq_Value=129000000
RCC.AHB12Freq_Value=240000000
RCC.AHB4Freq_Value=240000000
RCC.APB1Freq_Value=120000000
RCC.APB2Freq_Value=120000000
RCC.APB3Freq_Value=120000000
RCC.APB4Freq_Value=120000000
RCC.AXIClockFreq_Value=240000000
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=480000000
RCC.CpuClockFreq_Value=480000000
RCC.D1CPREFreq_Value=480000000
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=129000000
RCC.DFSDMFreq_Value=120000000
RCC.DIVM1=5
RCC.DIVN1=192
RCC.DIVP1Freq_Value=480000000
RCC.DIVP2Freq_Value=129000000
RCC.DIVP3Freq_Value=129000000
RCC.DIVQ1Freq_Value=480000000
RCC.DIVQ2Freq_Value=129000000
RCC.DIVQ3Freq_Value=129000000
RCC.DIVR1Freq_Value=480000000
RCC.DIVR2Freq_Value=129000000
RCC.DIVR3Freq_Value=129000000
RCC.FDCANFreq_Value=129000000
RCC.FMCFreq_Value=240000000
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=240000000
RCC.HCLKFreq_Value=240000000
RCC.HPRE=RCC_SYSCLK_DIV2
RCC.HRTIMFreq_Value=240000000
RCC.HSE_VALUE=25000000
RCC.I2C123Freq_Value=120000000
RCC.I2C4Freq_Value=120000000
RCC.LPTIM1Freq_Value=120000000
RCC.LPTIM2Freq_Value=120000000
RCC.LPTIM345Freq_Value=120000000
RCC.LPUART1Freq_Value=120000000
RCC.LTDCFreq_Value=129000000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=480000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.QSPIFreq_Value=240000000
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=129000000
RCC.SAI23Freq_Value=129000000
RCC.SAI4AFreq_Value=129000000
RCC.SAI4BFreq_Value=129000000
RCC.SDMMCFreq_Value=129000000
RCC.SPDIFRXFreq_Value=129000000
RCC.SPI123Freq_Value=129000000
RCC.SPI45Freq_Value=120000000
RCC.SPI6Freq_Value=120000000
RCC.SWPMI1Freq_Value=120000000
RCC.SYSCLKFreq_VALUE=480000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=240000000
RCC.Tim2OutputFreq_Value=240000000
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=120000000
RCC.USART234578Freq_Value=120000000
RCC.USBFreq_Value=129000000
RCC.VCO1OutputFreq_Value=960000000
RCC.VCO2OutputFreq_Value=258000000
RCC.VCO3OutputFreq_Value=258000000
RCC.VCOInput1Freq_Value=5000000
RCC.VCOInput2Freq_Value=2000000
RCC.VCOInput3Freq_Value=2000000
SH.COMP_DAC11_group.0=DAC1_OUT1,DAC_OUT1
SH.COMP_DAC11_group.ConfNb=1
SH.COMP_DAC12_group.0=DAC1_OUT2,DAC_OUT2
SH.COMP_DAC12_group.ConfNb=1
SH.FMC_A16_CLE.0=FMC_A16,16b-a1
SH.FMC_A16_CLE.ConfNb=1
SH.FMC_A17_ALE.0=FMC_A17,16b-a1
SH.FMC_A17_ALE.ConfNb=1
SH.FMC_A18.0=FMC_A18,16b-a1
SH.FMC_A18.ConfNb=1
SH.FMC_A24.0=FMC_A24,16b-a1
SH.FMC_A24.ConfNb=1
SH.FMC_A25.0=FMC_A25,16b-a1
SH.FMC_A25.ConfNb=1
SH.FMC_D0.0=FMC_D0,16b-d1
SH.FMC_D0.ConfNb=1
SH.FMC_D1.0=FMC_D1,16b-d1
SH.FMC_D1.ConfNb=1
SH.FMC_D10.0=FMC_D10,16b-d1
SH.FMC_D10.ConfNb=1
SH.FMC_D11.0=FMC_D11,16b-d1
SH.FMC_D11.ConfNb=1
SH.FMC_D12.0=FMC_D12,16b-d1
SH.FMC_D12.ConfNb=1
SH.FMC_D13.0=FMC_D13,16b-d1
SH.FMC_D13.ConfNb=1
SH.FMC_D14.0=FMC_D14,16b-d1
SH.FMC_D14.ConfNb=1
SH.FMC_D15.0=FMC_D15,16b-d1
SH.FMC_D15.ConfNb=1
SH.FMC_D2.0=FMC_D2,16b-d1
SH.FMC_D2.ConfNb=1
SH.FMC_D3.0=FMC_D3,16b-d1
SH.FMC_D3.ConfNb=1
SH.FMC_D4.0=FMC_D4,16b-d1
SH.FMC_D4.ConfNb=1
SH.FMC_D5.0=FMC_D5,16b-d1
SH.FMC_D5.ConfNb=1
SH.FMC_D6.0=FMC_D6,16b-d1
SH.FMC_D6.ConfNb=1
SH.FMC_D7.0=FMC_D7,16b-d1
SH.FMC_D7.ConfNb=1
SH.FMC_D8.0=FMC_D8,16b-d1
SH.FMC_D8.ConfNb=1
SH.FMC_D9.0=FMC_D9,16b-d1
SH.FMC_D9.ConfNb=1
SH.FMC_NBL0.0=FMC_NBL0,2ByteEnable1
SH.FMC_NBL0.ConfNb=1
SH.FMC_NBL1.0=FMC_NBL1,2ByteEnable1
SH.FMC_NBL1.ConfNb=1
SH.FMC_NCE3_2.0=FMC_NCE3_2,NorPsramChipSelect3_2
SH.FMC_NCE3_2.ConfNb=1
SH.FMC_NCE4_2.0=FMC_NCE4_2,NorPsramChipSelect4_2
SH.FMC_NCE4_2.ConfNb=1
SH.FMC_NE1.0=FMC_NE1,NorPsramChipSelect1_1
SH.FMC_NE1.ConfNb=1
SH.FMC_NE3.0=FMC_NE3,NorPsramChipSelect3_1
SH.FMC_NE3.ConfNb=1
SH.FMC_NE4.0=FMC_NE4,NorPsramChipSelect4_1
SH.FMC_NE4.ConfNb=1
SH.FMC_NOE.0=FMC_NOE,NorPsramChipSelect1_1
SH.FMC_NOE.ConfNb=1
SH.FMC_NWE.0=FMC_NWE,NorPsramChipSelect1_1
SH.FMC_NWE.ConfNb=1
SH.FMC_SDNCAS.0=FMC_SDNCAS,13b-sda1
SH.FMC_SDNCAS.ConfNb=1
USART1.IPParameters=VirtualMode-Asynchronous
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART2.IPParameters=VirtualMode-Asynchronous
USART2.VirtualMode-Asynchronous=VM_ASYNC
USART3.IPParameters=VirtualMode-Asynchronous
USART3.VirtualMode-Asynchronous=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
