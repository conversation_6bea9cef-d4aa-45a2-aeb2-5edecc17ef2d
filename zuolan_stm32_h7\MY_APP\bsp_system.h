/*******************************************************************************
 * @file      bsp_system.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     板级支持包(BSP)系统头文件 - STM32H743IIT6版本
 * @note      本文件作为整个项目的"总控制头文件"，负责包含所有其他需要用到的
 * 底层库文件、中间件以及自定义功能库以及应用模块头文件。同时声明了
 * 统一的、需要在多个模块间共享的全局变量。
 * 这种方式简化了文件包含关系，但需注意全局变量可能带来的耦合问题。
 *******************************************************************************/

#ifndef BSP_SYSTEM_H
#define BSP_SYSTEM_H

/*
****************************************************************************************
*
*
* 这是一个在开发过程中非常有用的"佛祖注释分割线"，采用标准ASCII字符绘制，
* 具有辟邪、镇宅、驱虫、避免崩溃、防止BUG、保佑编译通过等神奇功效。
* 有经验的程序员实际使用后，需要在本行开发者对代码稳定性进行的祈祷祝愿。
*
*                    _oo0oo_
*                   o8888888o
*                   88" . "88
*                   (| -_- |)
*                   0\  =  /0
*                 ___/`---'\___
*               .' \\|     |// '.
*              / \\|||  :  |||// \
*             / _||||| -:- |||||- \
*            |   | \\\  -  /// |   |
*            | \_|  ''\---/''  |_/ |
*            \  .-\__  '-'  ___/-.  /
*          ___'. .'  /--.--\  `. .'___
*       ."" '<  `.___\_<|>_/___.' >' "".
*      | | :  `- \`.;`\ _ /`;.`/ - ` : | |
*      \  \ `_.   \_ __\ /__ _/   .-` /  /
*  =====`-.____`.___ \_____/___.-`___.-'=====
*                    `=---='
*
*
*  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*
*           佛祖保佑       永无BUG
*
****************************************************************************************
*/

/* =========================== 包含的头文件列表 =========================== */

/* STM32 HAL库 和 C标准库 */
#include "main.h"         // STM32CubeMX生成的主头文件，包含基础定义
#include "stm32h7xx_hal.h"// STM32H7系列的HAL库核心文件 (从F4升级到H7)
#include "stdio.h"        // 标准输入输出库
#include "string.h"       // 字符串处理库
#include "stdarg.h"       // 可变参数库

/* 外设配置文件 (由STM32CubeMX生成或手动编写) */
#include "dac.h"          // 数模转换器(DAC)配置
#include "usart.h"        // 通用同步/异步收发器(USART)配置
#include "gpio.h"         // 通用输入输出(GPIO)配置
#include "fmc.h"          // 灵活存储控制器(FMC)配置，连接外部SRAM/SDRAM等

/* 中间件和算法库 */
#include "arm_math.h"     // ARM官方的CMSIS-DSP数学运算库
#include "scheduler.h"    // 一个简单的协作式实时任务片轮转调度模块
#include "my_fft.h"       // 自定义的快速傅里叶变换(FFT)处理模块
#include "my_filter.h"    // 自定义数字滤波器模块
#include "kalman.h"       // 卡尔曼滤波器模块

/* 自定义功能和应用层模块 */
#include "ad_measure.h"     // AD采样测量模块
#include "freq_measure.h"   // 频率测量模块
#include "phase_measure.h"  // 相位测量模块
#include "my_usart.h"       // 自定义的串口通信协议处理模块
#include "my_usart_pack.h"  // 自定义的串口数据打包/解包模块
#include "da_output.h"      // DA输出控制模块
#include "AD9959.h"         // AD9959 DDS芯片驱动模块
#include "commond_init.h"   // 通用初始化模块
#include "app_pid.h"        // PID控制器应用模块
#include "key_app.h"        // 按键应用处理模块
#include "AD9833.h"         // AD9833 DDS芯片驱动模块

/* =========================== 类型定义兼容性处理 =========================== */
// STM32H7的HAL库中某些类型定义可能与F4不同，这里做兼容性处理
#ifndef u8
typedef uint8_t  u8;
#endif

#ifndef u16
typedef uint16_t u16;
#endif

#ifndef u32
typedef uint32_t u32;
#endif

#ifndef s8
typedef int8_t   s8;
#endif

#ifndef s16
typedef int16_t  s16;
#endif

#ifndef s32
typedef int32_t  s32;
#endif

/* =========================== FMC地址映射定义 =========================== */
// STM32H7的FMC基地址可能与F4不同，需要根据实际硬件连接调整
#define FPGA_BASE_ADDR          0x64000000  // FPGA FMC基地址 (保持与F4版本一致)

// FPGA内部寄存器地址映射 (16个16位寄存器)
#define CTRL_DATA               ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x00))  // 控制数据寄存器
#define AD1_FS_H                ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x02))  // AD1采样频率高位
#define AD1_FS_L                ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x04))  // AD1采样频率低位
#define AD2_FS_H                ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x06))  // AD2采样频率高位
#define AD2_FS_L                ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x08))  // AD2采样频率低位
#define AD1_FREQ_H              ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x0A))  // AD1频率测量高位
#define AD1_FREQ_L              ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x0C))  // AD1频率测量低位
#define AD2_FREQ_H              ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x0E))  // AD2频率测量高位
#define AD2_FREQ_L              ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x10))  // AD2频率测量低位
#define PHASE_DIFF              ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x12))  // 相位差测量
#define DA_WAVE_CTRL            ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x14))  // DA波形控制
#define DA_FREQ_H               ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x16))  // DA频率高位
#define DA_FREQ_L               ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x18))  // DA频率低位
#define DA_PHASE                ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x1A))  // DA相位控制
#define DA_AMPLITUDE            ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x1C))  // DA幅度控制
#define RESERVED_REG1           ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x1E))  // 保留寄存器1
#define RESERVED_REG2           ((volatile uint16_t*)(FPGA_BASE_ADDR + 0x20))  // 保留寄存器2

/* =========================== 全局变量外部声明 =========================== */
// 使用 extern 关键字声明全局变量，这些变量实际定义在相应的.c源文件中。
// 包含了本头文件的工程中的任何包含了本头文件的模块都能访问这些变量

extern u32 Modulated_wave;      // 用于存储调制波数据的变量
extern u8 mdoe_flag;            // 系统模式标志 (可能是 "mode_flag" 的拼写错误)
extern u16 Phase;               // 存储当前系统相位值
extern u32 val;                 // 通用变量，常用于存储AD采样值或临时数据
extern u8 Carrier_indx;         // 载波索引 (可能是 "Carrier_index" 的拼写错误)
extern int32_t output;          // 输出变量，通常存储PID控制器的输出值
extern u32 pid_vin;             // PID控制器的输入反馈值
extern float detected_freq;     // 算法检测到的频率值

/* =========================== STM32H7特有的性能优化宏 =========================== */
// 利用STM32H7的高性能特性
#define ENABLE_CACHE()          do { \
                                    SCB_EnableICache(); \
                                    SCB_EnableDCache(); \
                                } while(0)

#define DISABLE_CACHE()         do { \
                                    SCB_DisableICache(); \
                                    SCB_DisableDCache(); \
                                } while(0)

// 内存屏障，确保关键操作的顺序性
#define MEMORY_BARRIER()        __DSB()

/* =========================== 调试和性能监控宏 =========================== */
#ifdef DEBUG
    #define DBG_PRINTF(fmt, ...)   printf("[DBG] " fmt "\r\n", ##__VA_ARGS__)
    #define ASSERT(expr)           do { if (!(expr)) { printf("ASSERT FAILED: %s:%d\r\n", __FILE__, __LINE__); while(1); } } while(0)
#else
    #define DBG_PRINTF(fmt, ...)   ((void)0)
    #define ASSERT(expr)           ((void)0)
#endif

// 性能计数器宏 (利用H7的DWT)
#define PERF_COUNTER_START()    do { DWT->CYCCNT = 0; } while(0)
#define PERF_COUNTER_GET()      (DWT->CYCCNT)

#endif /* BSP_SYSTEM_H */
