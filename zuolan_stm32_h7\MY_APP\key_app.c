/*******************************************************************************
 * @file      key_app.c
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     按键应用处理模块实现 - STM32H743IIT6版本
 *******************************************************************************/

#include "key_app.h"

// 按键状态变量
static uint8_t key_state[8] = {KEY_RELEASED};
static uint32_t key_press_time[8] = {0};

/**
 * @brief  按键处理任务函数
 * @param  None
 * @retval None
 */
void key_proc(void)
{
    // 扫描所有按键
    for (uint8_t i = 1; i <= 7; i++) {
        uint8_t current_state = key_scan(i);
        
        // 检测按键状态变化
        if (current_state != key_state[i]) {
            key_state[i] = current_state;
            
            if (current_state == KEY_PRESSED) {
                key_press_time[i] = HAL_GetTick();
                key_handler(i);
            }
        }
    }
}

/**
 * @brief  按键扫描函数
 * @param  key_num: 按键编号
 * @retval 按键状态
 */
uint8_t key_scan(uint8_t key_num)
{
    GPIO_TypeDef* port = GPIOB;
    uint16_t pin;
    
    switch (key_num) {
        case 1: pin = KEY1_Pin; break;
        case 2: pin = KEY2_Pin; break;
        case 3: pin = KEY3_Pin; break;
        case 4: pin = KEY4_Pin; break;
        case 5: pin = KEY5_Pin; break;
        case 6: pin = KEY6_Pin; break;
        case 7: pin = KEY7_Pin; break;
        default: return KEY_RELEASED;
    }
    
    return HAL_GPIO_ReadPin(port, pin);
}

/**
 * @brief  按键处理函数
 * @param  key_num: 按键编号
 * @retval None
 */
void key_handler(uint8_t key_num)
{
    // 简单的按键处理示例
    switch (key_num) {
        case 1:
            // 按键1：切换LED1
            HAL_GPIO_TogglePin(GPIOC, LED1_Pin);
            break;
        case 2:
            // 按键2：切换LED2
            HAL_GPIO_TogglePin(GPIOC, LED2_Pin);
            break;
        case 3:
            // 按键3：切换LED3
            HAL_GPIO_TogglePin(GPIOC, LED3_Pin);
            break;
        default:
            break;
    }
}
