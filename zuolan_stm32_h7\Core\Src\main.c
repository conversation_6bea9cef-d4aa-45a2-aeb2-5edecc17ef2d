/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body - STM32H743IIT6版本
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "dac.h"
#include "usart.h"
#include "gpio.h"
#include "fmc.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

#include "bsp_system.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
  /* USER CODE BEGIN 1 */
  uint16_t ASK_amp_ch2[16] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */
  
  /* 启用STM32H7的缓存系统以提升性能 */
  ENABLE_CACHE();
  
  /* 启用DWT性能计数器 */
  if (!(CoreDebug->DEMCR & CoreDebug_DEMCR_TRCENA_Msk)) {
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    DWT->CYCCNT = 0;
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
  }

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_FMC_Init();
  MX_USART1_UART_Init();
  MX_USART2_UART_Init();
  MX_USART3_UART_Init();
  MX_DAC1_Init();  // H7版本使用DAC1
  
  /* USER CODE BEGIN 2 */
  
  /* 启动串口中断接收 */
  HAL_UART_Receive_IT(&huart1, &rxTemp1, 1);
  HAL_UART_Receive_IT(&huart2, &rxTemp2, 1);
  HAL_UART_Receive_IT(&huart3, &rxTemp3, 1);
  
  /* 初始化各个功能模块 */
  CTRL_INIT();
  DA_Init();
  DA_Apply_Settings();
  PID_Init();
  fft_init();

  /* --- AD9959 DDS芯片初始化 --- */

  // 1. 初始化AD9959芯片 (复位, 配置PLL等基础参数)
  AD9959_Init();

  // 2. 配置通道2的调制模式:二电平调制(LEVEL_MOD_2), ASK调制(MOD_ASK)
  //    注意: 这里暂时禁用扫频功能,如需要可设置为 SWEEP_ENABLE
  AD9959_Modulation_Init(CH2_SELECT, LEVEL_MOD_2, MOD_ASK);

  // 3. 设置ASK调制的幅度数组
  //    当Profile选择器(P2)为低电平时,使用索引0,对应幅度为'0'
  ASK_amp_ch2[0] = 0;
  //    当Profile选择器(P2)为高电平时,使用Profile 0,对应幅度为'1'
  ASK_amp_ch2[1] = 1023; // 最大幅度值1023对应满幅输出

  // 4. 应用ASK设置:通道2输出1000Hz载波,并应用上述幅度设置
  //    注意:这里的频率、相位、幅度等参数可根据实际需求调整
  //    当前配置下 ASK_amp_ch2[0] 对应低电平输出, ASK_amp_ch2[1] 对应Profile 0的幅度
  AD9959_Set_ASK(CH2_SELECT, 1000, ASK_amp_ch2);

  // AD9833芯片配置示例 (如果需要使用)
  //AD9833_Setup(AD9833_REG_FREQ0, 30000.0, AD9833_REG_PHASE1, 1024, AD9833_OUT_TRIANGLE);
  //AD9833_Setup2(AD9833_REG_FREQ0, 30000.0, AD9833_REG_PHASE1, 2048, AD9833_OUT_TRIANGLE);
  
  /* 发送启动信息 */
  my_printf(&huart3, "\r\n STM32H743 LINK START! CPU@480MHz\r\n");
  my_printf(&huart3, "Cache Enabled, Performance Optimized\r\n");

  /* 初始化任务调度器 */
  scheduler_init();
  
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    /* 运行任务调度器 */
    scheduler_run();
    
    /* 可以在这里添加其他需要持续执行的代码 */
    
  }
  /* USER CODE END 3 */
}

/**
 * @brief System Clock Configuration for STM32H743
 * @retval None
 * @note  配置系统时钟到480MHz (相比F4的180MHz有显著提升)
 */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Supply configuration update enable
   */
  HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);

  /** Configure the main internal regulator output voltage
   */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}

  /** Initializes the RCC Oscillators according to the specified parameters
   * in the RCC_OscInitTypeDef structure.
   */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 5;      // 25MHz / 5 = 5MHz
  RCC_OscInitStruct.PLL.PLLN = 192;    // 5MHz * 192 = 960MHz
  RCC_OscInitStruct.PLL.PLLP = 2;      // 960MHz / 2 = 480MHz (系统时钟)
  RCC_OscInitStruct.PLL.PLLQ = 4;      // 960MHz / 4 = 240MHz
  RCC_OscInitStruct.PLL.PLLR = 2;      // 960MHz / 2 = 480MHz
  RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_2;
  RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
  RCC_OscInitStruct.PLL.PLLFRACN = 0;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
   */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
                              |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;      // 480MHz / 2 = 240MHz
  RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;     // 240MHz / 2 = 120MHz
  RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;     // 240MHz / 2 = 120MHz
  RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;     // 240MHz / 2 = 120MHz
  RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;     // 240MHz / 2 = 120MHz

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
