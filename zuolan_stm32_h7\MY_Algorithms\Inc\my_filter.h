/*******************************************************************************
 * @file      my_filter.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     数字滤波器模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __MY_FILTER_H__
#define __MY_FILTER_H__

#include "bsp_system.h"

// 滤波器类型定义
#define FILTER_TYPE_LPF     0   // 低通滤波器
#define FILTER_TYPE_HPF     1   // 高通滤波器
#define FILTER_TYPE_BPF     2   // 带通滤波器
#define FILTER_TYPE_BSF     3   // 带阻滤波器

// 滤波器配置结构体
typedef struct {
    uint8_t type;           // 滤波器类型
    float cutoff_freq;      // 截止频率
    float sampling_freq;    // 采样频率
    uint16_t order;         // 滤波器阶数
} filter_config_t;

// 函数声明
void filter_init(void);
float filter_process(float input);
void filter_reset(void);

#endif /* __MY_FILTER_H__ */
