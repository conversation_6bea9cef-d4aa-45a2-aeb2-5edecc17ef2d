/*******************************************************************************
 * @file      key_app.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     按键应用处理模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __KEY_APP_H__
#define __KEY_APP_H__

#include "bsp_system.h"

// 按键处理任务函数
void key_proc(void);

// 按键状态定义
#define KEY_PRESSED     0
#define KEY_RELEASED    1

// 按键编号定义
#define KEY1_NUM        1
#define KEY2_NUM        2
#define KEY3_NUM        3
#define KEY4_NUM        4
#define KEY5_NUM        5
#define KEY6_NUM        6
#define KEY7_NUM        7

// 按键处理函数
uint8_t key_scan(uint8_t key_num);
void key_handler(uint8_t key_num);

#endif /* __KEY_APP_H__ */
