/*******************************************************************************
 * @file      my_usart.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     该文件是 my_usart.c 的头文件 - STM32H743IIT6版本
 * @note      此文件定义了自定义串口驱动所需的宏、外部变量声明和函数原型。
 * 包含了对多个串口(USART1, 2, 3)共用的缓冲区大小定义，以及
 * 供其他模块调用的全局变量和函数。H7版本增加了DMA支持和性能优化。
 *******************************************************************************/

#ifndef __MY_USART_H__
#define __MY_USART_H__

#include "stm32h7xx_hal.h" // 包含STM32H7系列的HAL库头文件 (从F4升级到H7)
#include "usart.h"         // 包含CubeMX生成的usart初始化相关头文件
#include "stdint.h"        // 包含标准整型定义
#include "string.h"        // 字符串处理函数

// --- 宏定义 ---

#define RX_BUFFER_SIZE      256     ///< 定义每个串口接收缓冲区的统一大小，单位为字节 (H7版本增大缓冲区)
#define TX_BUFFER_SIZE      256     ///< 定义每个串口发送缓冲区的大小
#define MIN_FRAME_SIZE      3       ///< 定义一个有效数据帧的最小长度（例如：帧头+数据+帧尾）
#define MAX_FRAME_SIZE      128     ///< 定义一个数据帧的最大长度
#define UART_TIMEOUT_MS     1000    ///< 串口超时时间，单位毫秒

// 串口DMA配置
#define UART_DMA_ENABLE     1       ///< 启用DMA传输
#define UART_FIFO_ENABLE    1       ///< 启用FIFO模式 (H7特有)

// 调试配置
#define UART_DEBUG_ENABLE   1       ///< 启用串口调试功能
#define UART_STATS_ENABLE   1       ///< 启用串口统计功能

// --- 数据结构定义 ---

/**
 * @brief 串口统计信息结构体
 */
typedef struct {
    uint32_t tx_bytes;          ///< 发送字节数
    uint32_t rx_bytes;          ///< 接收字节数
    uint32_t tx_frames;         ///< 发送帧数
    uint32_t rx_frames;         ///< 接收帧数
    uint32_t tx_errors;         ///< 发送错误数
    uint32_t rx_errors;         ///< 接收错误数
    uint32_t overrun_errors;    ///< 溢出错误数
    uint32_t frame_errors;      ///< 帧错误数
    uint32_t noise_errors;      ///< 噪声错误数
    uint32_t parity_errors;     ///< 奇偶校验错误数
} uart_stats_t;

/**
 * @brief 串口缓冲区结构体
 */
typedef struct {
    uint8_t buffer[RX_BUFFER_SIZE]; ///< 缓冲区数据
    uint16_t head;                  ///< 头指针
    uint16_t tail;                  ///< 尾指针
    uint16_t count;                 ///< 当前数据量
    uint8_t overflow;               ///< 溢出标志
} uart_ring_buffer_t;

// --- 外部变量声明 ---
// 这些变量在 my_usart.c 中定义，此处声明以便在工程的其他文件中使用。

// 串口中断接收临时存储变量
extern uint8_t rxTemp1, rxTemp2, rxTemp3;

// 各串口接收数据的主缓冲区 (保持兼容性)
extern uint8_t rxBuffer1[RX_BUFFER_SIZE], rxBuffer2[RX_BUFFER_SIZE], rxBuffer3[RX_BUFFER_SIZE];

// 各串口接收缓冲区当前写入位置的索引
extern uint16_t rxIndex1, rxIndex2, rxIndex3;

// 串口接收到完整指令的标志位 (volatile 关键字防止编译器进行不当优化)
extern volatile uint8_t commandReceived1, commandReceived3;

// H7版本新增：环形缓冲区
extern uart_ring_buffer_t uart1_rx_ring, uart2_rx_ring, uart3_rx_ring;
extern uart_ring_buffer_t uart1_tx_ring, uart2_tx_ring, uart3_tx_ring;

// H7版本新增：统计信息
extern uart_stats_t uart1_stats, uart2_stats, uart3_stats;

// --- 函数原型声明 ---

/**
 * @brief  自定义的printf函数，可将格式化字符串通过指定的UART端口发送。
 * @param  huart:  目标串口的句柄指针。
 * @param  format: C语言格式化字符串。
 * @param  ...:    可变参数列表。
 * @retval int:    成功发送的字节数。
 */
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

/**
 * @brief  HAL库UART接收完成中断的公共回调函数。
 * @note   此函数是一个弱定义函数，在 my_usart.c 中被重写以实现自定义的接收逻辑。
 * 用户不应直接调用此函数。
 * @param  huart: 触发中断的串口句柄指针。
 * @retval None
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);

/**
 * @brief  HAL库UART错误回调函数
 * @param  huart: 发生错误的串口句柄指针
 * @retval None
 */
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart);

// --- H7版本新增函数 ---

/**
 * @brief  初始化串口环形缓冲区
 * @param  None
 * @retval None
 */
void uart_ring_buffer_init(void);

/**
 * @brief  向环形缓冲区写入数据
 * @param  ring: 环形缓冲区指针
 * @param  data: 要写入的数据
 * @retval 0: 成功, -1: 缓冲区满
 */
int uart_ring_buffer_put(uart_ring_buffer_t *ring, uint8_t data);

/**
 * @brief  从环形缓冲区读取数据
 * @param  ring: 环形缓冲区指针
 * @param  data: 读取数据的存储位置
 * @retval 0: 成功, -1: 缓冲区空
 */
int uart_ring_buffer_get(uart_ring_buffer_t *ring, uint8_t *data);

/**
 * @brief  获取环形缓冲区中的数据量
 * @param  ring: 环形缓冲区指针
 * @retval 缓冲区中的数据量
 */
uint16_t uart_ring_buffer_count(uart_ring_buffer_t *ring);

/**
 * @brief  清空环形缓冲区
 * @param  ring: 环形缓冲区指针
 * @retval None
 */
void uart_ring_buffer_clear(uart_ring_buffer_t *ring);

/**
 * @brief  发送数据（支持DMA）
 * @param  huart: 串口句柄
 * @param  data: 要发送的数据
 * @param  size: 数据长度
 * @retval HAL状态
 */
HAL_StatusTypeDef uart_transmit_dma(UART_HandleTypeDef *huart, uint8_t *data, uint16_t size);

/**
 * @brief  接收数据（支持DMA）
 * @param  huart: 串口句柄
 * @param  data: 接收数据的缓冲区
 * @param  size: 期望接收的数据长度
 * @retval HAL状态
 */
HAL_StatusTypeDef uart_receive_dma(UART_HandleTypeDef *huart, uint8_t *data, uint16_t size);

/**
 * @brief  获取串口统计信息
 * @param  huart: 串口句柄
 * @param  stats: 统计信息结构体指针
 * @retval 0: 成功, -1: 失败
 */
int uart_get_stats(UART_HandleTypeDef *huart, uart_stats_t *stats);

/**
 * @brief  重置串口统计信息
 * @param  huart: 串口句柄
 * @retval None
 */
void uart_reset_stats(UART_HandleTypeDef *huart);

/**
 * @brief  串口性能测试函数
 * @param  huart: 串口句柄
 * @param  test_size: 测试数据大小
 * @retval 传输速率 (bytes/s)
 */
uint32_t uart_performance_test(UART_HandleTypeDef *huart, uint32_t test_size);

/**
 * @brief  启用串口FIFO模式 (H7特有)
 * @param  huart: 串口句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef uart_enable_fifo(UART_HandleTypeDef *huart);

/**
 * @brief  配置串口高级特性 (H7特有)
 * @param  huart: 串口句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef uart_configure_advanced_features(UART_HandleTypeDef *huart);

#endif // __MY_USART_H__
