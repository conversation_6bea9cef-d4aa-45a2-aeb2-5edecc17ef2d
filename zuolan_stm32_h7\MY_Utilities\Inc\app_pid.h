/*******************************************************************************
 * @file      app_pid.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     PID控制器应用模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __APP_PID_H__
#define __APP_PID_H__

#include "bsp_system.h"

// PID参数结构体
typedef struct {
    float Kp;           // 比例系数
    float Ki;           // 积分系数
    float Kd;           // 微分系数
    float setpoint;     // 设定值
    float integral;     // 积分累积
    float prev_error;   // 上次误差
    float output_min;   // 输出最小值
    float output_max;   // 输出最大值
} pid_controller_t;

// 函数声明
void PID_Init(void);
void Pid_Proc(void);
float PID_Calculate(pid_controller_t *pid, float input);
void PID_Reset(pid_controller_t *pid);

#endif /* __APP_PID_H__ */
