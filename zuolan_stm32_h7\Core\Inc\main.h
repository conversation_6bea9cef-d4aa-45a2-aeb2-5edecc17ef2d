/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.h
 * @brief          : Header for main.c file - STM32H743IIT6版本
 *                   This file contains the common defines of the application.
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h7xx_hal.h"  // 从F4升级到H7

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */
/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
/* AD9833 DDS芯片引脚定义 */
#define AD9833_SDATA1_Pin GPIO_PIN_6
#define AD9833_SDATA1_GPIO_Port GPIOF
#define AD9833_SCLK1_Pin GPIO_PIN_8
#define AD9833_SCLK1_GPIO_Port GPIOF
#define AD9833_FSYNC1_Pin GPIO_PIN_9
#define AD9833_FSYNC1_GPIO_Port GPIOF
#define AD9833_SDATA2_Pin GPIO_PIN_1
#define AD9833_SDATA2_GPIO_Port GPIOC
#define AD9833_SCLK2_Pin GPIO_PIN_2
#define AD9833_SCLK2_GPIO_Port GPIOC
#define AD9833_FSYNC2_Pin GPIO_PIN_6
#define AD9833_FSYNC2_GPIO_Port GPIOC

/* AD9959 DDS芯片引脚定义 */
#define AD9959_P0_Pin GPIO_PIN_0
#define AD9959_P0_GPIO_Port GPIOI
#define AD9959_P1_Pin GPIO_PIN_9
#define AD9959_P1_GPIO_Port GPIOH
#define AD9959_P2_Pin GPIO_PIN_6
#define AD9959_P2_GPIO_Port GPIOG
#define AD9959_P3_Pin GPIO_PIN_0
#define AD9959_P3_GPIO_Port GPIOI
#define AD9959_IOUPDATE_Pin GPIO_PIN_11
#define AD9959_IOUPDATE_GPIO_Port GPIOA
#define AD9959_RESET_Pin GPIO_PIN_12
#define AD9959_RESET_GPIO_Port GPIOA
#define AD9959_CS_Pin GPIO_PIN_15
#define AD9959_CS_GPIO_Port GPIOA
#define AD9959_SCLK_Pin GPIO_PIN_8
#define AD9959_SCLK_GPIO_Port GPIOA
#define AD9959_SDIO0_Pin GPIO_PIN_12
#define AD9959_SDIO0_GPIO_Port GPIOB
#define AD9959_SDIO1_Pin GPIO_PIN_13
#define AD9959_SDIO1_GPIO_Port GPIOB
#define AD9959_SDIO2_Pin GPIO_PIN_14
#define AD9959_SDIO2_GPIO_Port GPIOB
#define AD9959_SDIO3_Pin GPIO_PIN_15
#define AD9959_SDIO3_GPIO_Port GPIOB

/* 按键引脚定义 */
#define KEY1_Pin GPIO_PIN_3
#define KEY1_GPIO_Port GPIOB
#define KEY2_Pin GPIO_PIN_4
#define KEY2_GPIO_Port GPIOB
#define KEY3_Pin GPIO_PIN_5
#define KEY3_GPIO_Port GPIOB
#define KEY4_Pin GPIO_PIN_6
#define KEY4_GPIO_Port GPIOB
#define KEY5_Pin GPIO_PIN_7
#define KEY5_GPIO_Port GPIOB
#define KEY6_Pin GPIO_PIN_8
#define KEY6_GPIO_Port GPIOB
#define KEY7_Pin GPIO_PIN_9
#define KEY7_GPIO_Port GPIOB

/* LED指示灯引脚定义 */
#define LED1_Pin GPIO_PIN_0
#define LED1_GPIO_Port GPIOC
#define LED2_Pin GPIO_PIN_1
#define LED2_GPIO_Port GPIOC
#define LED3_Pin GPIO_PIN_6
#define LED3_GPIO_Port GPIOC
#define LED4_Pin GPIO_PIN_7
#define LED4_GPIO_Port GPIOC
#define LED5_Pin GPIO_PIN_8
#define LED5_GPIO_Port GPIOC
#define LED6_Pin GPIO_PIN_9
#define LED6_GPIO_Port GPIOC
#define LED7_Pin GPIO_PIN_3
#define LED7_GPIO_Port GPIOD
#define LED8_Pin GPIO_PIN_6
#define LED8_GPIO_Port GPIOD

/* USER CODE BEGIN Private defines */

/* STM32H7特有的性能优化定义 */
#define CPU_CACHE_ENABLE        1    // 启用CPU缓存
#define DWT_ENABLE              1    // 启用DWT性能计数器
#define PREFETCH_ENABLE         1    // 启用指令预取

/* 系统性能参数 */
#define SYSTEM_CLOCK_FREQ       480000000UL  // 系统时钟频率 480MHz
#define AHB_CLOCK_FREQ          240000000UL  // AHB时钟频率 240MHz
#define APB1_CLOCK_FREQ         120000000UL  // APB1时钟频率 120MHz
#define APB2_CLOCK_FREQ         120000000UL  // APB2时钟频率 120MHz

/* 内存区域定义 */
#define DTCM_RAM_BASE           0x20000000UL // DTCM RAM基地址
#define DTCM_RAM_SIZE           0x00020000UL // DTCM RAM大小 128KB
#define AXI_SRAM_BASE           0x24000000UL // AXI SRAM基地址
#define AXI_SRAM_SIZE           0x00080000UL // AXI SRAM大小 512KB
#define SRAM1_BASE              0x30000000UL // SRAM1基地址
#define SRAM1_SIZE              0x00020000UL // SRAM1大小 128KB
#define SRAM2_BASE              0x30020000UL // SRAM2基地址
#define SRAM2_SIZE              0x00020000UL // SRAM2大小 128KB
#define SRAM3_BASE              0x30040000UL // SRAM3基地址
#define SRAM3_SIZE              0x00008000UL // SRAM3大小 32KB
#define SRAM4_BASE              0x38000000UL // SRAM4基地址
#define SRAM4_SIZE              0x00010000UL // SRAM4大小 64KB

/* 调试和性能监控 */
#ifdef DEBUG
    #define DEBUG_PRINTF_ENABLE 1
    #define ASSERT_ENABLE       1
    #define PERF_MONITOR_ENABLE 1
#else
    #define DEBUG_PRINTF_ENABLE 0
    #define ASSERT_ENABLE       0
    #define PERF_MONITOR_ENABLE 0
#endif

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
