/*******************************************************************************
 * @file      AD9833.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     AD9833 DDS芯片驱动头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __AD9833_H__
#define __AD9833_H__

#include "bsp_system.h"

// AD9833寄存器定义
#define AD9833_REG_FREQ0    0x4000
#define AD9833_REG_FREQ1    0x8000
#define AD9833_REG_PHASE0   0xC000
#define AD9833_REG_PHASE1   0xE000

// 输出波形类型
#define AD9833_OUT_SINE     0
#define AD9833_OUT_TRIANGLE 1
#define AD9833_OUT_SQUARE   2

// 函数声明
void AD9833_Init(void);
void AD9833_Setup(uint16_t freq_reg, float frequency, uint16_t phase_reg, uint16_t phase, uint8_t wave_type);
void AD9833_Setup2(uint16_t freq_reg, float frequency, uint16_t phase_reg, uint16_t phase, uint8_t wave_type);

#endif /* __AD9833_H__ */
