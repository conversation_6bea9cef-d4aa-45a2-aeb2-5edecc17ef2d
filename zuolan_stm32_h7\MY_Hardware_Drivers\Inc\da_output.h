/*******************************************************************************
 * @file      da_output.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     DA输出控制模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __DA_OUTPUT_H__
#define __DA_OUTPUT_H__

#include "bsp_system.h"

// 函数声明
void DA_Init(void);
void DA_Apply_Settings(void);
void DA_SetOutput(uint8_t channel, uint16_t value);

#endif /* __DA_OUTPUT_H__ */
