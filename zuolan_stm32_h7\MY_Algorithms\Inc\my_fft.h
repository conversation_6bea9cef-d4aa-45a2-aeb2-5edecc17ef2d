/*******************************************************************************
 * @file      my_fft.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     FFT频谱分析模块的头文件 - STM32H743IIT6版本
 * @note      此文件定义了FFT模块的配置（如点数），声明了所需的全局数据
 * 缓冲区，并提供了所有公共函数的外部接口。H7版本利用双精度FPU
 * 和更大内存，支持更高精度和更大点数的FFT计算。
 *******************************************************************************/

#ifndef __MY_FFT_H
#define __MY_FFT_H

#include "bsp_system.h"
#include "arm_math.h" // 包含ARM CMSIS-DSP库，用于FFT相关函数

// --------------------------- 配置宏定义 ---------------------------

// 定义FFT的计算点数。
// H7版本支持更大的FFT点数，利用更大的内存容量
// 该值必须是2的幂次方。对于本工程使用的基4(Radix-4)算法，最好是4的幂次方
#define FFT_LENGTH_SMALL    1024    // 小点数FFT，用于实时处理
#define FFT_LENGTH_MEDIUM   4096    // 中等点数FFT，用于高精度分析
#define FFT_LENGTH_LARGE    16384   // 大点数FFT，用于超高精度分析

// 默认使用的FFT长度
#define FFT_LENGTH          FFT_LENGTH_MEDIUM

// FFT精度配置
#define FFT_USE_DOUBLE      1       // 启用双精度FFT (H7特有)
#define FFT_USE_CACHE       1       // 启用缓存优化
#define FFT_USE_PARALLEL    1       // 启用并行计算优化

// 窗函数类型定义
#define WINDOW_NONE         0       // 矩形窗
#define WINDOW_HANNING      1       // 汉宁窗
#define WINDOW_HAMMING      2       // 汉明窗
#define WINDOW_BLACKMAN     3       // 布莱克曼窗
#define WINDOW_KAISER       4       // 凯泽窗

#define DEFAULT_WINDOW      WINDOW_HANNING

// 频谱分析配置
#define MAX_HARMONICS       10      // 最大谐波数量
#define NOISE_FLOOR_DB      -120.0f // 噪声底限 (dB)
#define PEAK_THRESHOLD_DB   -60.0f  // 峰值检测阈值 (dB)

// 内存优化配置
#define FFT_USE_DTCM        1       // 使用DTCM内存以提升性能
#define FFT_USE_AXI_SRAM    1       // 使用AXI SRAM存储大数据

// --------------------------- 数据类型定义 ---------------------------

/**
 * @brief FFT配置结构体
 */
typedef struct {
    uint16_t fft_size;              // FFT点数
    uint8_t window_type;            // 窗函数类型
    float sampling_freq;            // 采样频率
    uint8_t use_double_precision;   // 是否使用双精度
    uint8_t enable_cache;           // 是否启用缓存
} fft_config_t;

/**
 * @brief 频谱分析结果结构体
 */
typedef struct {
    float peak_frequency;           // 主峰频率
    float peak_magnitude;           // 主峰幅度
    float thd;                      // 总谐波失真
    float thd_n;                    // THD+N
    float sinad;                    // 信纳比
    float snr;                      // 信噪比
    float noise_floor;              // 噪声底限
    uint16_t peak_bin;              // 主峰对应的频率仓
    float harmonics[MAX_HARMONICS]; // 谐波频率
    float harmonic_magnitudes[MAX_HARMONICS]; // 谐波幅度
} fft_result_t;

/**
 * @brief FFT性能统计结构体
 */
typedef struct {
    uint32_t calculation_count;     // 计算次数
    uint32_t total_cycles;          // 总计算周期
    uint32_t max_cycles;            // 最大计算周期
    uint32_t min_cycles;            // 最小计算周期
    uint32_t avg_cycles;            // 平均计算周期
    float avg_calculation_time_ms;  // 平均计算时间 (毫秒)
} fft_performance_t;

// --------------------------- 全局变量声明 ---------------------------
// 使用extern关键字声明全局变量，这些变量在my_fft.c中定义，可供其他模块访问。

// 单精度FFT缓冲区
extern float fft_input_buffer[FFT_LENGTH * 2];     // FFT输入复数缓冲区 (实部、虚部交错存储)
extern float fft_magnitude[FFT_LENGTH];            // 存储FFT计算后的幅度谱
extern float window_buffer[FFT_LENGTH];            // 存储预计算的窗函数系数

// H7版本新增：双精度FFT缓冲区
#if FFT_USE_DOUBLE
extern double fft_input_buffer_double[FFT_LENGTH * 2];
extern double fft_magnitude_double[FFT_LENGTH];
extern double window_buffer_double[FFT_LENGTH];
#endif

// H7版本新增：大点数FFT缓冲区 (存储在AXI SRAM中)
#if FFT_USE_AXI_SRAM
extern float fft_large_input_buffer[FFT_LENGTH_LARGE * 2] __attribute__((section(".axi_sram")));
extern float fft_large_magnitude[FFT_LENGTH_LARGE] __attribute__((section(".axi_sram")));
#endif

// FFT配置和结果
extern fft_config_t fft_config;
extern fft_result_t fft_result;
extern fft_performance_t fft_performance;

// ARM CMSIS-DSP FFT实例
extern arm_rfft_fast_instance_f32 fft_instance;
extern arm_cfft_radix4_instance_f32 cfft_instance;

#if FFT_USE_DOUBLE
extern arm_rfft_instance_f64 fft_instance_double;
#endif

// --------------------------- 外部函数声明 ---------------------------

/** @brief FFT模块初始化函数声明 */
void fft_init(void);

/** @brief 执行FFT频谱计算函数声明 */
void calculate_fft_spectrum(float* input_data, uint16_t data_length);

/** @brief 通过串口输出频谱分析结果函数声明 */
void output_fft_spectrum(void);

/** @brief 生成指定类型窗函数声明 */
void generate_window(uint8_t window_type);

/** @brief 生成Hanning窗函数声明 (通常为内部调用) */
void generate_hanning_window(void);

/** @brief 使用抛物线插值法获取精确峰值频率函数声明 */
float get_precise_peak_frequency(float sampling_freq);

/** @brief 将频率圆整到最近的1kHz函数声明 */
float round_to_nearest_k(float frequency);

/** @brief 计算总谐波失真(THD)函数声明 */
float calculate_thd(float fundamental_freq, float sampling_freq);

/** @brief 计算THD+N（总谐波失真加噪声）函数声明 */
float calculate_thd_n(float fundamental_freq, float sampling_freq);

/** @brief 计算信纳比(SINAD)函数声明 */
float calculate_sinad(float fundamental_freq, float sampling_freq);

// --------------------------- H7版本新增函数 ---------------------------

/** @brief 双精度FFT计算函数 */
#if FFT_USE_DOUBLE
void calculate_fft_spectrum_double(double* input_data, uint16_t data_length);
#endif

/** @brief 大点数FFT计算函数 */
void calculate_fft_spectrum_large(float* input_data, uint32_t data_length);

/** @brief 配置FFT参数 */
int fft_configure(fft_config_t* config);

/** @brief 获取FFT计算结果 */
int fft_get_result(fft_result_t* result);

/** @brief 获取FFT性能统计 */
int fft_get_performance(fft_performance_t* performance);

/** @brief 重置FFT性能统计 */
void fft_reset_performance(void);

/** @brief 自动选择最优FFT配置 */
int fft_auto_configure(float sampling_freq, uint16_t signal_length);

/** @brief FFT缓存管理 */
void fft_cache_clean(void);
void fft_cache_invalidate(void);

/** @brief 并行FFT计算 (利用H7的双核特性，如果可用) */
void calculate_fft_spectrum_parallel(float* input_data, uint16_t data_length);

/** @brief 实时频谱分析 */
void fft_realtime_analysis(float* input_data, uint16_t data_length, fft_result_t* result);

/** @brief 频谱比较分析 */
float fft_spectrum_compare(float* spectrum1, float* spectrum2, uint16_t length);

/** @brief 频谱平滑处理 */
void fft_spectrum_smooth(float* spectrum, uint16_t length, uint8_t smooth_factor);

/** @brief 峰值检测 */
int fft_peak_detection(float* spectrum, uint16_t length, float* peaks, uint16_t max_peaks);

/** @brief 谐波分析 */
int fft_harmonic_analysis(float fundamental_freq, float sampling_freq, fft_result_t* result);

/** @brief 频谱瀑布图数据生成 */
void fft_waterfall_update(float* new_spectrum, uint16_t spectrum_length);

/** @brief 自适应窗函数选择 */
uint8_t fft_adaptive_window_selection(float* input_data, uint16_t data_length);

/** @brief FFT质量评估 */
float fft_quality_assessment(fft_result_t* result);

#endif /* __MY_FFT_H */
