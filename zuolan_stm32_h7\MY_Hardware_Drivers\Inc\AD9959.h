/*******************************************************************************
 * @file      AD9959.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     AD9959 DDS芯片驱动头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __AD9959_H__
#define __AD9959_H__

#include "bsp_system.h"

// AD9959通道选择
#define CH0_SELECT      0x01
#define CH1_SELECT      0x02
#define CH2_SELECT      0x04
#define CH3_SELECT      0x08

// 调制类型
#define MOD_ASK         0
#define MOD_FSK         1
#define MOD_PSK         2

// 调制级别
#define LEVEL_MOD_2     0
#define LEVEL_MOD_4     1
#define LEVEL_MOD_8     2

// 扫频控制
#define SWEEP_ENABLE    1
#define SWEEP_DISABLE   0

// 函数声明
void AD9959_Init(void);
void AD9959_Modulation_Init(uint8_t channel, uint8_t level, uint8_t mod_type);
void AD9959_Set_ASK(uint8_t channel, uint32_t frequency, uint16_t *amplitude);
void AD9959_Modulation_State_Update(void);
void AD9959_proc(void);

#endif /* __AD9959_H__ */
