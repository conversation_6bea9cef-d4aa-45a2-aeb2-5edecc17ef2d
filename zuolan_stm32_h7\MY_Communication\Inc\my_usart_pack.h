/*******************************************************************************
 * @file      my_usart_pack.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     串口数据打包/解包模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __MY_USART_PACK_H__
#define __MY_USART_PACK_H__

#include "bsp_system.h"

// 数据包结构定义
typedef struct {
    uint8_t header;     // 包头
    uint8_t cmd;        // 命令
    uint8_t length;     // 数据长度
    uint8_t data[64];   // 数据
    uint8_t checksum;   // 校验和
} data_packet_t;

// 函数声明
void pack_data(data_packet_t *packet, uint8_t cmd, uint8_t *data, uint8_t length);
int unpack_data(uint8_t *buffer, uint16_t buffer_length, data_packet_t *packet);
uint8_t calculate_checksum(uint8_t *data, uint16_t length);

#endif /* __MY_USART_PACK_H__ */
