/*******************************************************************************
 * @file      system_test.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     系统测试模块头文件 - STM32H743IIT6版本
 * @note      此文件定义了系统功能验证和性能测试的接口函数，用于验证
 * 移植后的各项功能是否正常工作。
 *******************************************************************************/

#ifndef __SYSTEM_TEST_H__
#define __SYSTEM_TEST_H__

#include "bsp_system.h"

/*******************************************************************************
 * 测试配置宏定义
 *******************************************************************************/

#define TEST_ENABLE             1       // 启用测试功能
#define TEST_VERBOSE            1       // 详细测试输出
#define TEST_PERFORMANCE        1       // 性能测试
#define TEST_STRESS             0       // 压力测试 (可选)

// 测试超时时间 (毫秒)
#define TEST_TIMEOUT_MS         5000

// 测试数据大小
#define TEST_DATA_SIZE          1024
#define TEST_FFT_SIZE           1024
#define TEST_FILTER_SIZE        256

/*******************************************************************************
 * 测试结果枚举
 *******************************************************************************/

typedef enum {
    TEST_RESULT_PASS = 0,       // 测试通过
    TEST_RESULT_FAIL,           // 测试失败
    TEST_RESULT_TIMEOUT,        // 测试超时
    TEST_RESULT_ERROR,          // 测试错误
    TEST_RESULT_SKIP            // 测试跳过
} test_result_t;

/*******************************************************************************
 * 测试统计结构体
 *******************************************************************************/

typedef struct {
    uint16_t total_tests;       // 总测试数
    uint16_t passed_tests;      // 通过测试数
    uint16_t failed_tests;      // 失败测试数
    uint16_t skipped_tests;     // 跳过测试数
    uint32_t total_time_ms;     // 总测试时间
    float pass_rate;            // 通过率
} test_statistics_t;

/*******************************************************************************
 * 性能测试结果结构体
 *******************************************************************************/

typedef struct {
    uint32_t cpu_freq_mhz;      // CPU频率
    uint32_t fft_time_us;       // FFT计算时间 (微秒)
    uint32_t filter_time_us;    // 滤波器时间 (微秒)
    uint32_t pid_time_us;       // PID计算时间 (微秒)
    uint32_t uart_bps;          // 串口传输速率
    uint32_t fmc_bps;           // FMC传输速率
    float memory_usage_percent; // 内存使用率
} performance_result_t;

/*******************************************************************************
 * 外部函数声明
 *******************************************************************************/

/**
 * @brief  系统测试初始化
 * @param  None
 * @retval None
 */
void system_test_init(void);

/**
 * @brief  运行所有系统测试
 * @param  None
 * @retval test_result_t 总体测试结果
 */
test_result_t system_test_run_all(void);

/**
 * @brief  基础硬件测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_basic_hardware(void);

/**
 * @brief  时钟系统测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_clock_system(void);

/**
 * @brief  内存系统测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_memory_system(void);

/**
 * @brief  缓存系统测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_cache_system(void);

/**
 * @brief  串口通信测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_uart_communication(void);

/**
 * @brief  FMC接口测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_fmc_interface(void);

/**
 * @brief  GPIO功能测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_gpio_functions(void);

/**
 * @brief  DAC输出测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_dac_output(void);

/**
 * @brief  FFT算法测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_fft_algorithm(void);

/**
 * @brief  滤波器算法测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_filter_algorithm(void);

/**
 * @brief  PID控制器测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_pid_controller(void);

/**
 * @brief  任务调度器测试
 * @param  None
 * @retval test_result_t 测试结果
 */
test_result_t test_task_scheduler(void);

/**
 * @brief  性能基准测试
 * @param  result 性能测试结果指针
 * @retval test_result_t 测试结果
 */
test_result_t test_performance_benchmark(performance_result_t *result);

/**
 * @brief  压力测试
 * @param  duration_ms 测试持续时间 (毫秒)
 * @retval test_result_t 测试结果
 */
test_result_t test_stress_test(uint32_t duration_ms);

/**
 * @brief  获取测试统计信息
 * @param  stats 统计信息指针
 * @retval None
 */
void system_test_get_statistics(test_statistics_t *stats);

/**
 * @brief  重置测试统计
 * @param  None
 * @retval None
 */
void system_test_reset_statistics(void);

/**
 * @brief  输出测试报告
 * @param  None
 * @retval None
 */
void system_test_print_report(void);

/**
 * @brief  比较F429和H743性能
 * @param  None
 * @retval None
 */
void system_test_compare_performance(void);

/**
 * @brief  验证移植正确性
 * @param  None
 * @retval test_result_t 验证结果
 */
test_result_t system_test_verify_migration(void);

/*******************************************************************************
 * 测试辅助宏定义
 *******************************************************************************/

#define TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            my_printf(&huart3, "[FAIL] %s: %s\r\n", __FUNCTION__, message); \
            return TEST_RESULT_FAIL; \
        } \
    } while(0)

#define TEST_ASSERT_EQUAL(expected, actual, message) \
    do { \
        if ((expected) != (actual)) { \
            my_printf(&huart3, "[FAIL] %s: %s (expected: %d, actual: %d)\r\n", \
                      __FUNCTION__, message, (int)(expected), (int)(actual)); \
            return TEST_RESULT_FAIL; \
        } \
    } while(0)

#define TEST_ASSERT_RANGE(value, min, max, message) \
    do { \
        if ((value) < (min) || (value) > (max)) { \
            my_printf(&huart3, "[FAIL] %s: %s (value: %d, range: %d-%d)\r\n", \
                      __FUNCTION__, message, (int)(value), (int)(min), (int)(max)); \
            return TEST_RESULT_FAIL; \
        } \
    } while(0)

#define TEST_START(test_name) \
    do { \
        my_printf(&huart3, "[TEST] Starting %s...\r\n", test_name); \
        uint32_t test_start_time = HAL_GetTick(); \
    } while(0)

#define TEST_END(test_name, result) \
    do { \
        uint32_t test_end_time = HAL_GetTick(); \
        uint32_t test_duration = test_end_time - test_start_time; \
        const char* result_str = (result == TEST_RESULT_PASS) ? "PASS" : "FAIL"; \
        my_printf(&huart3, "[%s] %s completed in %lu ms\r\n", \
                  result_str, test_name, test_duration); \
    } while(0)

#endif /* __SYSTEM_TEST_H__ */
