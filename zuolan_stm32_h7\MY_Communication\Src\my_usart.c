/*******************************************************************************
 * @file      my_usart.c
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     自定义串口驱动实现 - STM32H743IIT6版本
 * @note      此文件实现了自定义的串口通信功能，包括格式化输出、中断接收
 * 处理等。H7版本增加了DMA支持和性能优化。
 *******************************************************************************/

#include "my_usart.h"
#include <stdarg.h>
#include <stdio.h>

/*******************************************************************************
 * 全局变量定义
 *******************************************************************************/

// 串口中断接收临时存储变量
uint8_t rxTemp1, rxTemp2, rxTemp3;

// 各串口接收数据的主缓冲区
uint8_t rxBuffer1[RX_BUFFER_SIZE], rxBuffer2[RX_BUFFER_SIZE], rxBuffer3[RX_BUFFER_SIZE];

// 各串口接收缓冲区当前写入位置的索引
uint16_t rxIndex1, rxIndex2, rxIndex3;

// 串口接收到完整指令的标志位
volatile uint8_t commandReceived1, commandReceived3;

// H7版本新增：环形缓冲区
uart_ring_buffer_t uart1_rx_ring, uart2_rx_ring, uart3_rx_ring;
uart_ring_buffer_t uart1_tx_ring, uart2_tx_ring, uart3_tx_ring;

// H7版本新增：统计信息
uart_stats_t uart1_stats, uart2_stats, uart3_stats;

/*******************************************************************************
 * 私有函数声明
 *******************************************************************************/

static uart_stats_t* get_uart_stats(UART_HandleTypeDef *huart);
static void update_rx_stats(UART_HandleTypeDef *huart, uint16_t size);
static void update_tx_stats(UART_HandleTypeDef *huart, uint16_t size);

/*******************************************************************************
 * 公共函数实现
 *******************************************************************************/

/**
 * @brief  自定义的printf函数，可将格式化字符串通过指定的UART端口发送
 * @param  huart:  目标串口的句柄指针
 * @param  format: C语言格式化字符串
 * @param  ...:    可变参数列表
 * @retval int:    成功发送的字节数
 */
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[256];  // 临时缓冲区，用于存储格式化后的字符串
    va_list args;      // 可变参数列表
    int length;        // 格式化后字符串的长度

    // 初始化可变参数列表
    va_start(args, format);
    
    // 使用vsnprintf进行格式化，并获取字符串长度
    length = vsnprintf(buffer, sizeof(buffer), format, args);
    
    // 清理可变参数列表
    va_end(args);

    // 确保长度不超过缓冲区大小
    if (length > sizeof(buffer) - 1) {
        length = sizeof(buffer) - 1;
    }

    // 通过HAL库发送数据
    if (HAL_UART_Transmit(huart, (uint8_t*)buffer, length, UART_TIMEOUT_MS) == HAL_OK) {
        // 更新发送统计
        update_tx_stats(huart, length);
        return length;
    }

    return 0;  // 发送失败返回0
}

/**
 * @brief  HAL库UART接收完成中断的公共回调函数
 * @note   此函数重写了HAL库的弱定义函数，实现自定义的接收逻辑
 * @param  huart: 触发中断的串口句柄指针
 * @retval None
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        // 将接收到的字节存入缓冲区
        rxBuffer1[rxIndex1] = rxTemp1;
        rxIndex1++;
        
        // 检查缓冲区是否溢出
        if (rxIndex1 >= RX_BUFFER_SIZE) {
            rxIndex1 = 0;  // 环形缓冲区，回到开头
        }
        
        // 检查是否接收到完整指令（以回车换行结尾）
        if (rxTemp1 == '\n' || rxTemp1 == '\r') {
            commandReceived1 = 1;
        }
        
        // 更新接收统计
        update_rx_stats(huart, 1);
        
        // 重新启动接收中断
        HAL_UART_Receive_IT(huart, &rxTemp1, 1);
    }
    else if (huart->Instance == USART2) {
        // USART2的处理逻辑
        rxBuffer2[rxIndex2] = rxTemp2;
        rxIndex2++;
        
        if (rxIndex2 >= RX_BUFFER_SIZE) {
            rxIndex2 = 0;
        }
        
        update_rx_stats(huart, 1);
        HAL_UART_Receive_IT(huart, &rxTemp2, 1);
    }
    else if (huart->Instance == USART3) {
        // USART3的处理逻辑
        rxBuffer3[rxIndex3] = rxTemp3;
        rxIndex3++;
        
        if (rxIndex3 >= RX_BUFFER_SIZE) {
            rxIndex3 = 0;
        }
        
        if (rxTemp3 == '\n' || rxTemp3 == '\r') {
            commandReceived3 = 1;
        }
        
        update_rx_stats(huart, 1);
        HAL_UART_Receive_IT(huart, &rxTemp3, 1);
    }
}

/**
 * @brief  HAL库UART错误回调函数
 * @param  huart: 发生错误的串口句柄指针
 * @retval None
 */
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    uart_stats_t *stats = get_uart_stats(huart);
    if (stats != NULL) {
        // 根据错误类型更新统计
        if (huart->ErrorCode & HAL_UART_ERROR_ORE) {
            stats->overrun_errors++;
        }
        if (huart->ErrorCode & HAL_UART_ERROR_FE) {
            stats->frame_errors++;
        }
        if (huart->ErrorCode & HAL_UART_ERROR_NE) {
            stats->noise_errors++;
        }
        if (huart->ErrorCode & HAL_UART_ERROR_PE) {
            stats->parity_errors++;
        }
    }
    
    // 清除错误标志并重新启动接收
    __HAL_UART_CLEAR_OREFLAG(huart);
    __HAL_UART_CLEAR_FEFLAG(huart);
    __HAL_UART_CLEAR_NEFLAG(huart);
    __HAL_UART_CLEAR_PEFLAG(huart);
    
    // 重新启动接收中断
    if (huart->Instance == USART1) {
        HAL_UART_Receive_IT(huart, &rxTemp1, 1);
    } else if (huart->Instance == USART2) {
        HAL_UART_Receive_IT(huart, &rxTemp2, 1);
    } else if (huart->Instance == USART3) {
        HAL_UART_Receive_IT(huart, &rxTemp3, 1);
    }
}

/**
 * @brief  初始化串口环形缓冲区
 * @param  None
 * @retval None
 */
void uart_ring_buffer_init(void)
{
    // 清零所有环形缓冲区
    memset(&uart1_rx_ring, 0, sizeof(uart_ring_buffer_t));
    memset(&uart2_rx_ring, 0, sizeof(uart_ring_buffer_t));
    memset(&uart3_rx_ring, 0, sizeof(uart_ring_buffer_t));
    memset(&uart1_tx_ring, 0, sizeof(uart_ring_buffer_t));
    memset(&uart2_tx_ring, 0, sizeof(uart_ring_buffer_t));
    memset(&uart3_tx_ring, 0, sizeof(uart_ring_buffer_t));
    
    // 清零统计信息
    memset(&uart1_stats, 0, sizeof(uart_stats_t));
    memset(&uart2_stats, 0, sizeof(uart_stats_t));
    memset(&uart3_stats, 0, sizeof(uart_stats_t));
}

/**
 * @brief  获取串口统计信息
 * @param  huart: 串口句柄
 * @param  stats: 统计信息结构体指针
 * @retval 0: 成功, -1: 失败
 */
int uart_get_stats(UART_HandleTypeDef *huart, uart_stats_t *stats)
{
    uart_stats_t *src_stats = get_uart_stats(huart);
    if (src_stats == NULL || stats == NULL) {
        return -1;
    }
    
    *stats = *src_stats;
    return 0;
}

/**
 * @brief  重置串口统计信息
 * @param  huart: 串口句柄
 * @retval None
 */
void uart_reset_stats(UART_HandleTypeDef *huart)
{
    uart_stats_t *stats = get_uart_stats(huart);
    if (stats != NULL) {
        memset(stats, 0, sizeof(uart_stats_t));
    }
}

/*******************************************************************************
 * 私有函数实现
 *******************************************************************************/

/**
 * @brief  获取指定串口的统计信息指针
 * @param  huart: 串口句柄
 * @retval 统计信息指针，失败返回NULL
 */
static uart_stats_t* get_uart_stats(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        return &uart1_stats;
    } else if (huart->Instance == USART2) {
        return &uart2_stats;
    } else if (huart->Instance == USART3) {
        return &uart3_stats;
    }
    return NULL;
}

/**
 * @brief  更新接收统计信息
 * @param  huart: 串口句柄
 * @param  size: 接收字节数
 * @retval None
 */
static void update_rx_stats(UART_HandleTypeDef *huart, uint16_t size)
{
    uart_stats_t *stats = get_uart_stats(huart);
    if (stats != NULL) {
        stats->rx_bytes += size;
        if (size > 0) {
            stats->rx_frames++;
        }
    }
}

/**
 * @brief  更新发送统计信息
 * @param  huart: 串口句柄
 * @param  size: 发送字节数
 * @retval None
 */
static void update_tx_stats(UART_HandleTypeDef *huart, uint16_t size)
{
    uart_stats_t *stats = get_uart_stats(huart);
    if (stats != NULL) {
        stats->tx_bytes += size;
        if (size > 0) {
            stats->tx_frames++;
        }
    }
}
