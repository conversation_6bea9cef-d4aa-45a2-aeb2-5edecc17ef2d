/*******************************************************************************
 * @file      kalman.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     卡尔曼滤波器模块头文件 - STM32H743IIT6版本
 *******************************************************************************/

#ifndef __KALMAN_H__
#define __KALMAN_H__

#include "bsp_system.h"

// 卡尔曼滤波器结构体
typedef struct {
    float Q;        // 过程噪声协方差
    float R;        // 测量噪声协方差
    float P;        // 估计误差协方差
    float K;        // 卡尔曼增益
    float x;        // 状态估计值
} kalman_filter_t;

// 函数声明
void kalman_init(kalman_filter_t *kf, float Q, float R, float P, float initial_value);
float kalman_update(kalman_filter_t *kf, float measurement);

#endif /* __KALMAN_H__ */
