/*******************************************************************************
 * @file      scheduler.h
 * <AUTHOR>
 * @version   V2.0 (STM32H7移植版本)
 * @date      2025-07-30
 * @brief     简单任务调度器的头文件 - STM32H743IIT6版本
 * @note      此文件为任务调度器模块提供了外部接口声明。任何需要使用
 * 该调度器的模块，都应包含此头文件来调用其初始化和运行函数。
 * H7版本增加了性能监控和优化功能。
 *******************************************************************************/

#ifndef SCHEDULER_H
#define SCHEDULER_H

// 包含系统级头文件，以获取必要的类型定义或宏，如 u8, u32 等。
#include "bsp_system.h"

/*******************************************************************************
 * 调度器配置参数
 *******************************************************************************/

#define MAX_TASKS               16      // 最大任务数量
#define SCHEDULER_TICK_MS       1       // 调度器时基，单位毫秒
#define TASK_STACK_CHECK        1       // 启用任务栈检查
#define TASK_PERFORMANCE_MONITOR 1      // 启用任务性能监控

/*******************************************************************************
 * 任务状态定义
 *******************************************************************************/
typedef enum {
    TASK_STATE_READY = 0,       // 任务就绪
    TASK_STATE_RUNNING,         // 任务运行中
    TASK_STATE_SUSPENDED,       // 任务挂起
    TASK_STATE_BLOCKED          // 任务阻塞
} task_state_t;

/*******************************************************************************
 * 任务性能统计结构
 *******************************************************************************/
typedef struct {
    uint32_t run_count;         // 运行次数
    uint32_t total_cycles;      // 总执行周期数
    uint32_t max_cycles;        // 最大执行周期数
    uint32_t min_cycles;        // 最小执行周期数
    uint32_t avg_cycles;        // 平均执行周期数
} task_perf_t;

/*******************************************************************************
 * 外部函数声明
 *******************************************************************************/

/**
 * @brief  调度器初始化函数声明
 * @see    scheduler.c 文件中的具体实现
 */
void scheduler_init(void);

/**
 * @brief  调度器主运行函数声明
 * @see    scheduler.c 文件中的具体实现
 */
void scheduler_run(void);

/**
 * @brief  获取任务性能统计信息
 * @param  task_id 任务ID
 * @param  perf 性能统计结构指针
 * @retval 0: 成功, -1: 失败
 */
int scheduler_get_task_performance(uint8_t task_id, task_perf_t *perf);

/**
 * @brief  重置任务性能统计
 * @param  task_id 任务ID (0xFF表示重置所有任务)
 */
void scheduler_reset_performance(uint8_t task_id);

/**
 * @brief  挂起指定任务
 * @param  task_id 任务ID
 * @retval 0: 成功, -1: 失败
 */
int scheduler_suspend_task(uint8_t task_id);

/**
 * @brief  恢复指定任务
 * @param  task_id 任务ID
 * @retval 0: 成功, -1: 失败
 */
int scheduler_resume_task(uint8_t task_id);

/**
 * @brief  获取系统负载百分比
 * @retval 系统负载 (0-100)
 */
uint8_t scheduler_get_cpu_load(void);

#endif /* SCHEDULER_H */
